-- Database setup for Kisah Cinta Malaysia game
CREATE DATABASE IF NOT EXISTS kisah_cinta_malaysia;
USE kisah_cinta_malaysia;

-- Players table
CREATE TABLE IF NOT EXISTS players (
    player_id INT AUTO_INCREMENT PRIMARY KEY,
    player_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Player progress table
CREATE TABLE IF NOT EXISTS player_progress (
    progress_id INT AUTO_INCREMENT PRIMARY KEY,
    player_id INT NOT NULL,
    character_id VARCHAR(50) NOT NULL,
    relationship_level INT DEFAULT 0,
    story_index INT DEFAULT 0,
    choices_made JSON,
    last_played TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (player_id) REFERENCES players(player_id) ON DELETE CASCADE,
    UNIQUE KEY unique_player_character (player_id, character_id)
);

-- Game statistics table
CREATE TABLE IF NOT EXISTS game_statistics (
    stat_id INT AUTO_INCREMENT PRIMARY KEY,
    player_id INT NOT NULL,
    total_playtime INT DEFAULT 0, -- in minutes
    games_completed INT DEFAULT 0,
    highest_relationship INT DEFAULT 0,
    favorite_character VARCHAR(50),
    achievements JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (player_id) REFERENCES players(player_id) ON DELETE CASCADE
);

-- Character information table
CREATE TABLE IF NOT EXISTS characters (
    character_id VARCHAR(50) PRIMARY KEY,
    character_name VARCHAR(100) NOT NULL,
    description TEXT,
    personality JSON,
    background TEXT,
    interests JSON,
    favorite_places JSON,
    avatar_emoji VARCHAR(10),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default characters
INSERT INTO characters (character_id, character_name, description, personality, background, interests, favorite_places, avatar_emoji) VALUES
('aisyah', 'Aisyah', 'Pelajar universiti yang bijak dan sopan', 
 '["Pandai", "Lembut", "Kreatif"]',
 'Aisyah adalah pelajar tahun akhir jurusan Sastera Melayu. Dia berasal dari Kelantan dan sangat menghargai budaya tradisional Malaysia.',
 '["Membaca", "Menulis puisi", "Seni kaligrafi", "Budaya tradisional"]',
 '["Perpustakaan", "Muzium", "Taman bunga", "Kafe buku"]',
 '👩‍🎓'),

('siti', 'Siti', 'Pekerja pejabat yang berjaya dan berdikari',
 '["Profesional", "Kuat", "Bersemangat"]',
 'Siti adalah eksekutif pemasaran digital yang berjaya. Dia berasal dari Kuala Lumpur dan sangat berdedikasi dalam kerjayanya.',
 '["Teknologi", "Pemasaran", "Fotografi", "Perjalanan"]',
 '["Kafe moden", "Pusat bandar", "Galeri seni", "Co-working space"]',
 '👩‍💼'),

('fatimah', 'Fatimah', 'Chef berbakat yang suka memasak',
 '["Pandai masak", "Ceria", "Penyayang"]',
 'Fatimah adalah chef yang berbakat dan pemilik restoran kecil. Dia berasal dari Penang dan mahir dalam masakan tradisional Malaysia.',
 '["Memasak", "Mencuba resepi baru", "Berkebun", "Mengajar masakan"]',
 '["Dapur", "Pasar", "Restoran tradisional", "Kebun herba"]',
 '👩‍🍳');

-- Story content table
CREATE TABLE IF NOT EXISTS story_content (
    story_id INT AUTO_INCREMENT PRIMARY KEY,
    character_id VARCHAR(50) NOT NULL,
    story_index INT NOT NULL,
    story_text TEXT NOT NULL,
    choices JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (character_id) REFERENCES characters(character_id) ON DELETE CASCADE,
    UNIQUE KEY unique_character_story (character_id, story_index)
);

-- Insert story content
INSERT INTO story_content (character_id, story_index, story_text, choices) VALUES
('aisyah', 0, 
 'Anda bertemu Aisyah di perpustakaan universiti. Dia sedang membaca buku tebal tentang sastera Melayu. Matanya yang jernih memandang anda dengan senyuman manis.',
 '[
    {"text": "Tanya tentang buku yang dibacanya", "relationship": 10},
    {"text": "Duduk diam-diam di sebelahnya", "relationship": 5},
    {"text": "Tegur dengan mesra", "relationship": 15}
 ]'),

('aisyah', 1,
 'Aisyah tersenyum lembut. \"Saya suka membaca puisi-puisi lama. Ada keindahan dalam kata-kata yang tersusun rapi,\" katanya sambil menunjukkan halaman buku.',
 '[
    {"text": "Berkongsi minat anda terhadap sastera", "relationship": 20},
    {"text": "Tanya pendapatnya tentang penulis kegemaran", "relationship": 15},
    {"text": "Ajak dia ke kafe untuk berbual lagi", "relationship": 25}
 ]'),

('siti', 0,
 'Anda bertemu Siti di sebuah kafe dekat pejabat. Dia sedang bekerja dengan laptop sambil minum kopi. Penampilannya profesional dan percaya diri.',
 '[
    {"text": "Tanya tentang pekerjaannya", "relationship": 10},
    {"text": "Puji dedikasinya bekerja", "relationship": 15},
    {"text": "Tawarkan belanja kopi", "relationship": 12}
 ]'),

('fatimah', 0,
 'Anda bertemu Fatimah di pasar malam. Dia sedang memilih bahan-bahan segar untuk masakan. Senyumannya ceria dan ramah kepada semua penjual.',
 '[
    {"text": "Tanya tentang masakan yang akan dimasak", "relationship": 12},
    {"text": "Tawarkan bantuan membawa barang belian", "relationship": 15},
    {"text": "Puji kemahirannya memilih bahan segar", "relationship": 18}
 ]');

-- Achievements table
CREATE TABLE IF NOT EXISTS achievements (
    achievement_id INT AUTO_INCREMENT PRIMARY KEY,
    achievement_name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(10),
    requirement_type VARCHAR(50), -- 'relationship', 'story_complete', 'all_characters', etc.
    requirement_value INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert achievements
INSERT INTO achievements (achievement_name, description, icon, requirement_type, requirement_value) VALUES
('Perkenalan Pertama', 'Bertemu dengan karakter pertama', '👋', 'story_complete', 1),
('Sahabat Baik', 'Mencapai tahap hubungan 50%', '🤝', 'relationship', 50),
('Kekasih Sejati', 'Mencapai tahap hubungan 100%', '💕', 'relationship', 100),
('Pengumpul Cerita', 'Selesaikan semua cerita dengan satu karakter', '📚', 'all_stories', 1),
('Hati Yang Luas', 'Berkenalan dengan semua karakter', '❤️', 'all_characters', 3);

-- Player achievements table
CREATE TABLE IF NOT EXISTS player_achievements (
    player_id INT NOT NULL,
    achievement_id INT NOT NULL,
    earned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (player_id, achievement_id),
    FOREIGN KEY (player_id) REFERENCES players(player_id) ON DELETE CASCADE,
    FOREIGN KEY (achievement_id) REFERENCES achievements(achievement_id) ON DELETE CASCADE
);

-- Game settings table
CREATE TABLE IF NOT EXISTS game_settings (
    player_id INT PRIMARY KEY,
    bgm_volume INT DEFAULT 50,
    sfx_volume INT DEFAULT 70,
    text_speed VARCHAR(20) DEFAULT 'normal',
    language VARCHAR(10) DEFAULT 'ms',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (player_id) REFERENCES players(player_id) ON DELETE CASCADE
);
