// Game State
let gameState = {
    currentCharacter: null,
    relationshipLevel: 0,
    currentStoryIndex: 0,
    playerName: '<PERSON>'
};

// Character Data
const characters = {
    aisyah: {
        name: '<PERSON><PERSON><PERSON>',
        avatar: '👩‍🎓',
        description: 'Pelajar universiti yang bijak dan sopan',
        personality: ['pandai', 'lembut', 'kreatif'],
        stories: [
            {
                text: "Anda bertemu <PERSON>ah di perpustakaan universiti. Dia sedang membaca buku tebal tentang sastera Melayu. Matanya yang jernih memandang anda dengan senyuman manis.",
                choices: [
                    { text: "Tanya tentang buku yang dibacanya", relationship: +10 },
                    { text: "Duduk diam-diam di sebelahnya", relationship: +5 },
                    { text: "Tegur dengan mesra", relationship: +15 }
                ]
            },
            {
                text: "Aisyah tersenyum lembut. 'Saya suka membaca puisi-puisi lama. Ada keindahan dalam kata-kata yang tersusun rapi,' katanya sambil menunjukkan halaman buku.",
                choices: [
                    { text: "Berkongsi minat anda terhadap sastera", relationship: +20 },
                    { text: "Tanya pendapatnya tentang penulis kegemaran", relationship: +15 },
                    { text: "Ajak dia ke kafe untuk berbual lagi", relationship: +25 }
                ]
            },
            {
                text: "Di kafe, Aisyah bercerita tentang impiannya menjadi guru. 'Saya nak ajar budak-budak supaya mereka cinta pada bahasa kita,' katanya dengan penuh semangat.",
                choices: [
                    { text: "Puji cita-citanya yang mulia", relationship: +20 },
                    { text: "Tanya tentang rancangan masa depannya", relationship: +15 },
                    { text: "Tawarkan bantuan untuk mencapai impiannya", relationship: +30 }
                ]
            }
        ]
    },
    siti: {
        name: 'Siti',
        avatar: '👩‍💼',
        description: 'Pekerja pejabat yang berjaya dan berdikari',
        personality: ['profesional', 'kuat', 'bersemangat'],
        stories: [
            {
                text: "Anda bertemu Siti di sebuah kafe dekat pejabat. Dia sedang bekerja dengan laptop sambil minum kopi. Penampilannya profesional dan percaya diri.",
                choices: [
                    { text: "Tanya tentang pekerjaannya", relationship: +10 },
                    { text: "Puji dedikasinya bekerja", relationship: +15 },
                    { text: "Tawarkan belanja kopi", relationship: +12 }
                ]
            },
            {
                text: "Siti mengangkat kepala dari laptopnya. 'Saya kerja dalam bidang pemasaran digital. Memang mencabar, tapi saya suka cabaran,' katanya dengan yakin.",
                choices: [
                    { text: "Tanya tentang projek yang sedang dijalankan", relationship: +18 },
                    { text: "Berkongsi pengalaman kerja anda", relationship: +20 },
                    { text: "Ajak dia berehat sebentar dari kerja", relationship: +15 }
                ]
            },
            {
                text: "Siti menutup laptopnya dan tersenyum. 'Terima kasih kerana ingatkan saya untuk berehat. Kadang-kadang kita terlalu fokus sampai lupa jaga diri,' katanya.",
                choices: [
                    { text: "Ajak dia jalan-jalan di taman berdekatan", relationship: +25 },
                    { text: "Cadangkan aktiviti santai untuk hujung minggu", relationship: +22 },
                    { text: "Tanya tentang hobi dia selain kerja", relationship: +18 }
                ]
            }
        ]
    },
    fatimah: {
        name: 'Fatimah',
        avatar: '👩‍🍳',
        description: 'Chef berbakat yang suka memasak',
        personality: ['pandai masak', 'ceria', 'penyayang'],
        stories: [
            {
                text: "Anda bertemu Fatimah di pasar malam. Dia sedang memilih bahan-bahan segar untuk masakan. Senyumannya ceria dan ramah kepada semua penjual.",
                choices: [
                    { text: "Tanya tentang masakan yang akan dimasak", relationship: +12 },
                    { text: "Tawarkan bantuan membawa barang belian", relationship: +15 },
                    { text: "Puji kemahirannya memilih bahan segar", relationship: +18 }
                ]
            },
            {
                text: "Fatimah ketawa riang. 'Saya nak masak rendang untuk keluarga esok. Kena pilih daging yang terbaik,' katanya sambil menunjukkan daging yang dipilihnya.",
                choices: [
                    { text: "Tanya resepi rendang keluarganya", relationship: +20 },
                    { text: "Cerita tentang masakan kegemaran anda", relationship: +15 },
                    { text: "Minta dia ajar masak rendang", relationship: +25 }
                ]
            },
            {
                text: "Mata Fatimah berbinar-binar. 'Saya suka mengajar orang masak! Kalau nak, boleh datang rumah esok. Saya tunjuk cara buat rendang yang sedap,' katanya dengan gembira.",
                choices: [
                    { text: "Terima jemputan dengan gembira", relationship: +30 },
                    { text: "Tanya apa yang boleh dibawa untuk membantu", relationship: +25 },
                    { text: "Puji kebaikan hatinya", relationship: +20 }
                ]
            }
        ]
    }
};

// Screen Management
function showScreen(screenId) {
    document.querySelectorAll('.screen').forEach(screen => {
        screen.classList.remove('active');
    });
    document.getElementById(screenId).classList.add('active');
}

function showMainMenu() {
    showScreen('main-menu');
}

function showCharacters() {
    showScreen('character-screen');
}

function showSettings() {
    showScreen('settings-screen');
}

function showCredits() {
    showScreen('credits-screen');
}

// Game Functions
function startNewGame() {
    showCharacters();
}

function selectCharacter(characterId) {
    gameState.currentCharacter = characterId;
    gameState.relationshipLevel = 0;
    gameState.currentStoryIndex = 0;
    
    const character = characters[characterId];
    document.getElementById('character-name').textContent = character.name;
    document.querySelector('.character-sprite').textContent = character.avatar;
    
    updateRelationshipMeter();
    loadCurrentStory();
    showScreen('game-screen');
}

function updateRelationshipMeter() {
    const percentage = Math.min(gameState.relationshipLevel, 100);
    document.getElementById('relationship-bar').style.width = percentage + '%';
    document.getElementById('relationship-level').textContent = percentage + '%';
}

function loadCurrentStory() {
    const character = characters[gameState.currentCharacter];
    const currentStory = character.stories[gameState.currentStoryIndex];
    
    if (!currentStory) {
        // End of story
        document.getElementById('story-text').innerHTML = `
            <h3>Tamat Cerita</h3>
            <p>Terima kasih kerana bermain! Tahap hubungan anda dengan ${character.name}: ${gameState.relationshipLevel}%</p>
            <p>Anda telah mengenali ${character.name} dengan lebih dekat. Setiap pilihan yang anda buat telah membentuk hubungan yang unik.</p>
        `;
        document.getElementById('choices').innerHTML = '';
        document.getElementById('next-btn').style.display = 'none';
        return;
    }
    
    // Display story text
    document.getElementById('story-text').textContent = currentStory.text;
    
    // Display choices
    const choicesContainer = document.getElementById('choices');
    choicesContainer.innerHTML = '';
    
    currentStory.choices.forEach((choice, index) => {
        const button = document.createElement('button');
        button.className = 'choice-btn';
        button.textContent = choice.text;
        button.onclick = () => makeChoice(index);
        choicesContainer.appendChild(button);
    });
    
    document.getElementById('next-btn').style.display = 'none';
}

function makeChoice(choiceIndex) {
    const character = characters[gameState.currentCharacter];
    const currentStory = character.stories[gameState.currentStoryIndex];
    const choice = currentStory.choices[choiceIndex];
    
    // Update relationship
    gameState.relationshipLevel += choice.relationship;
    gameState.relationshipLevel = Math.max(0, Math.min(100, gameState.relationshipLevel));
    
    updateRelationshipMeter();
    
    // Show choice result
    document.getElementById('story-text').innerHTML = `
        <p><strong>Anda memilih:</strong> ${choice.text}</p>
        <p><em>Hubungan ${choice.relationship > 0 ? 'bertambah' : 'berkurang'} ${Math.abs(choice.relationship)} mata!</em></p>
    `;
    
    // Hide choices and show next button
    document.getElementById('choices').innerHTML = '';
    document.getElementById('next-btn').style.display = 'inline-block';
}

function nextStory() {
    gameState.currentStoryIndex++;
    loadCurrentStory();
}

// PHP Integration Functions
async function saveProgress() {
    const formData = new FormData();
    formData.append('action', 'save_progress');
    formData.append('player_id', 1); // Default player for now
    formData.append('character_id', gameState.currentCharacter);
    formData.append('relationship_level', gameState.relationshipLevel);
    formData.append('story_index', gameState.currentStoryIndex);

    try {
        const response = await fetch('game_data.php', {
            method: 'POST',
            body: formData
        });
        const result = await response.json();
        console.log('Progress saved:', result);
    } catch (error) {
        console.error('Error saving progress:', error);
    }
}

async function loadProgress(characterId) {
    const formData = new FormData();
    formData.append('action', 'load_progress');
    formData.append('player_id', 1);
    formData.append('character_id', characterId);

    try {
        const response = await fetch('game_data.php', {
            method: 'POST',
            body: formData
        });
        const progress = await response.json();

        if (progress.relationship_level) {
            gameState.relationshipLevel = parseInt(progress.relationship_level);
            gameState.currentStoryIndex = parseInt(progress.story_index);
            updateRelationshipMeter();
        }
    } catch (error) {
        console.error('Error loading progress:', error);
    }
}

// Enhanced choice making with save functionality
function makeChoice(choiceIndex) {
    const character = characters[gameState.currentCharacter];
    const currentStory = character.stories[gameState.currentStoryIndex];
    const choice = currentStory.choices[choiceIndex];

    // Update relationship
    gameState.relationshipLevel += choice.relationship;
    gameState.relationshipLevel = Math.max(0, Math.min(100, gameState.relationshipLevel));

    updateRelationshipMeter();

    // Save progress to database
    saveProgress();

    // Show choice result with more detailed feedback
    let relationshipText = '';
    if (choice.relationship > 20) {
        relationshipText = 'Dia sangat terkesan dengan pilihan anda! 💕';
    } else if (choice.relationship > 10) {
        relationshipText = 'Dia suka dengan pendekatan anda! 😊';
    } else if (choice.relationship > 0) {
        relationshipText = 'Dia menghargai usaha anda. 🙂';
    } else {
        relationshipText = 'Mungkin ada cara yang lebih baik... 😅';
    }

    document.getElementById('story-text').innerHTML = `
        <p><strong>Anda memilih:</strong> ${choice.text}</p>
        <p><em>${relationshipText}</em></p>
        <p><em>Hubungan ${choice.relationship > 0 ? 'bertambah' : 'berkurang'} ${Math.abs(choice.relationship)} mata!</em></p>
    `;

    // Hide choices and show next button
    document.getElementById('choices').innerHTML = '';
    document.getElementById('next-btn').style.display = 'inline-block';
}

// Enhanced character selection with progress loading
async function selectCharacter(characterId) {
    gameState.currentCharacter = characterId;
    gameState.relationshipLevel = 0;
    gameState.currentStoryIndex = 0;

    // Load saved progress
    await loadProgress(characterId);

    const character = characters[characterId];
    document.getElementById('character-name').textContent = character.name;
    document.querySelector('.character-sprite').textContent = character.avatar;

    updateRelationshipMeter();
    loadCurrentStory();
    showScreen('game-screen');
}

// Initialize game
document.addEventListener('DOMContentLoaded', function() {
    showMainMenu();

    // Add some interactive effects
    document.querySelectorAll('.menu-btn').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.02)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Auto-save progress periodically
    setInterval(() => {
        if (gameState.currentCharacter) {
            saveProgress();
        }
    }, 30000); // Save every 30 seconds
});

// Settings functionality
document.addEventListener('DOMContentLoaded', function() {
    const bgmVolume = document.getElementById('bgm-volume');
    const sfxVolume = document.getElementById('sfx-volume');
    const textSpeed = document.getElementById('text-speed');

    if (bgmVolume) {
        bgmVolume.addEventListener('input', function() {
            // Handle background music volume
            console.log('BGM Volume:', this.value);
        });
    }

    if (sfxVolume) {
        sfxVolume.addEventListener('input', function() {
            // Handle sound effects volume
            console.log('SFX Volume:', this.value);
        });
    }

    if (textSpeed) {
        textSpeed.addEventListener('change', function() {
            // Handle text speed change
            console.log('Text Speed:', this.value);
        });
    }
});
