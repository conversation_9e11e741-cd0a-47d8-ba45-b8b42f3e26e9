# Kisah <PERSON>inta <PERSON> - Game Romantik

Sebuah permainan cerita romantik interaktif yang menampilkan budaya Malaysia dengan penuh hormat dan kesopanan.

## Ciri-ciri Permainan

### 🎮 Gameplay
- **Visual Novel Style**: Cerita interaktif dengan pilihan yang mempengaruhi jalan cerita
- **Sistem Hubungan**: Meter hubungan yang berubah berdasarkan pilihan pemain
- **Multiple Characters**: 3 karakter wanita dengan personaliti dan latar belakang yang berbeza
- **Penyimpanan Progress**: Kemajuan permainan disimpan dalam database

### 👥 Karakter
1. **Aisyah** - Pelajar universiti yang bijak dan sopan
   - Minat: Sastera, puisi, budaya tradisional
   - Personaliti: Pandai, lembut, kreatif

2. **Siti** - Pekerja pejabat yang berjaya dan berdikari
   - Minat: Teknologi, pemasaran, fotografi
   - Personaliti: Profesional, kuat, bersemangat

3. **Fatimah** - Chef berbakat yang suka memasak
   - Minat: Memasak, resepi baru, berkebun
   - Personaliti: Pandai masak, ceria, penyayang

### 🛠 Teknologi
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Backend**: PHP 7.4+
- **Database**: MySQL/MariaDB
- **Design**: Responsive design dengan Flexbox/Grid

## Pemasangan

### Keperluan Sistem
- Web server (Apache/Nginx)
- PHP 7.4 atau lebih tinggi
- MySQL/MariaDB
- Browser moden (Chrome, Firefox, Safari, Edge)

### Langkah Pemasangan

1. **Clone atau muat turun projek**
   ```bash
   git clone [repository-url]
   cd kisah-cinta-malaysia
   ```

2. **Setup Database**
   - Buat database MySQL baru
   - Import fail `database.sql`
   ```sql
   mysql -u root -p < database.sql
   ```

3. **Konfigurasi Database**
   - Edit `game_data.php`
   - Kemaskini maklumat sambungan database:
   ```php
   $host = 'localhost';
   $dbname = 'kisah_cinta_malaysia';
   $username = 'your_username';
   $password = 'your_password';
   ```

4. **Setup Web Server**
   - Letakkan fail dalam direktori web server
   - Pastikan PHP dan MySQL berjalan
   - Akses `index.html` melalui browser

## Struktur Fail

```
kisah-cinta-malaysia/
├── index.html          # Halaman utama permainan
├── style.css           # Styling dan design
├── script.js           # Logic permainan JavaScript
├── game_data.php       # Backend PHP dan database functions
├── api.php             # API endpoints untuk AJAX
├── database.sql        # Schema database
└── README.md           # Dokumentasi
```

## Cara Bermain

1. **Menu Utama**: Pilih "Permainan Baru" untuk mula bermain
2. **Pilih Karakter**: Pilih salah satu daripada 3 karakter yang tersedia
3. **Baca Cerita**: Ikuti jalan cerita yang dipaparkan
4. **Buat Pilihan**: Pilih respons yang anda rasa sesuai
5. **Lihat Kemajuan**: Meter hubungan akan berubah berdasarkan pilihan anda
6. **Simpan Progress**: Kemajuan akan disimpan secara automatik

## Ciri-ciri Tambahan

### 📊 Sistem Pencapaian
- Perkenalan Pertama
- Sahabat Baik (50% hubungan)
- Kekasih Sejati (100% hubungan)
- Pengumpul Cerita
- Hati Yang Luas

### ⚙️ Tetapan
- Kawalan bunyi latar
- Kawalan bunyi kesan
- Kelajuan teks
- Bahasa (Bahasa Malaysia)

### 🏆 Papan Pendahulu
- Ranking berdasarkan tahap hubungan
- Statistik pemain
- Perbandingan kemajuan

## Pengembangan Masa Depan

- [ ] Lebih banyak karakter
- [ ] Cerita yang lebih panjang dan kompleks
- [ ] Sistem mini-games
- [ ] Muzik latar dan bunyi kesan
- [ ] Animasi karakter
- [ ] Mode multiplayer
- [ ] Sistem hadiah dan koleksi

## Sokongan

Jika anda menghadapi masalah:
1. Pastikan semua keperluan sistem dipenuhi
2. Semak sambungan database
3. Lihat console browser untuk error JavaScript
4. Pastikan PHP error reporting diaktifkan

## Lesen

Projek ini dibuat untuk tujuan pembelajaran dan hiburan. Sila hormati budaya dan nilai-nilai Malaysia.

---

**Selamat Bermain! 🎮💕**
