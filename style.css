* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.game-container {
    width: 100%;
    min-height: 100vh;
    position: relative;
}

/* Screen Management */
.screen {
    display: none;
    width: 100%;
    min-height: 100vh;
    padding: 20px;
}

.screen.active {
    display: block;
}

/* Main Menu Styles */
#main-menu {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), 
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hearts" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><text x="10" y="15" text-anchor="middle" fill="rgba(255,255,255,0.1)" font-size="12">💕</text></pattern></defs><rect width="100" height="100" fill="url(%23hearts)"/></svg>');
}

.menu-background {
    background: rgba(255, 255, 255, 0.95);
    padding: 60px 40px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    text-align: center;
    max-width: 500px;
    width: 100%;
}

.game-title {
    font-size: 3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.game-subtitle {
    font-size: 1.2rem;
    color: #7f8c8d;
    margin-bottom: 40px;
    font-weight: 300;
}

.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.menu-btn {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.menu-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(231, 76, 60, 0.3);
}

.menu-btn .icon {
    font-size: 1.3rem;
}

/* Screen Header */
.screen-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 15px;
}

.screen-header h2 {
    color: #2c3e50;
    font-size: 2rem;
    font-weight: 600;
}

.back-btn {
    background: #95a5a6;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.back-btn:hover {
    background: #7f8c8d;
    transform: translateY(-1px);
}

/* Character Selection */
.character-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.character-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.character-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.character-avatar {
    font-size: 4rem;
    margin-bottom: 20px;
}

.character-card h3 {
    color: #2c3e50;
    font-size: 1.5rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.character-card p {
    color: #7f8c8d;
    margin-bottom: 20px;
    line-height: 1.6;
}

.character-traits {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.trait {
    background: #e8f4fd;
    color: #2980b9;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Game Interface */
.game-interface {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    overflow: hidden;
    max-width: 1000px;
    margin: 0 auto;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.character-display {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    padding: 30px;
    display: flex;
    align-items: center;
    gap: 30px;
    color: white;
}

.character-image {
    width: 120px;
    height: 120px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
}

.character-info h3 {
    font-size: 2rem;
    margin-bottom: 15px;
    font-weight: 600;
}

.relationship-meter {
    display: flex;
    align-items: center;
    gap: 15px;
    font-weight: 500;
}

.meter {
    width: 200px;
    height: 10px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    overflow: hidden;
}

.meter-fill {
    height: 100%;
    background: #00b894;
    width: 0%;
    transition: width 0.5s ease;
    border-radius: 5px;
}

.story-box {
    padding: 40px;
}

.story-text {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 15px;
    font-size: 1.1rem;
    line-height: 1.8;
    color: #2c3e50;
    margin-bottom: 30px;
    min-height: 150px;
    border-left: 5px solid #74b9ff;
}

.choices {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 30px;
}

.choice-btn {
    background: #74b9ff;
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 10px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    text-align: left;
}

.choice-btn:hover {
    background: #0984e3;
    transform: translateX(5px);
}

.game-controls {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.control-btn {
    background: #2d3436;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: #636e72;
    transform: translateY(-1px);
}

/* Settings */
.settings-content {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    max-width: 600px;
    margin: 0 auto;
}

.setting-item {
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.setting-item label {
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.1rem;
}

.setting-item input, .setting-item select {
    padding: 8px 15px;
    border: 2px solid #ddd;
    border-radius: 10px;
    font-size: 1rem;
}

/* Credits */
.credits-content {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    max-width: 600px;
    margin: 0 auto;
    text-align: center;
}

.credits-content h3 {
    color: #2c3e50;
    font-size: 2rem;
    margin-bottom: 20px;
    font-weight: 600;
}

.credit-section {
    margin: 30px 0;
}

.credit-section h4 {
    color: #74b9ff;
    font-size: 1.3rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.credit-section p {
    color: #7f8c8d;
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-title {
        font-size: 2rem;
    }
    
    .character-display {
        flex-direction: column;
        text-align: center;
    }
    
    .character-grid {
        grid-template-columns: 1fr;
    }
    
    .menu-background {
        margin: 20px;
        padding: 40px 20px;
    }
    
    .relationship-meter {
        flex-direction: column;
        gap: 10px;
    }
    
    .meter {
        width: 150px;
    }
}
