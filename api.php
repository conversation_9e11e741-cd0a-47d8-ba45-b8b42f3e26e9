<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Include database connection
require_once 'game_data.php';

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// API endpoint handler
$method = $_SERVER['REQUEST_METHOD'];
$endpoint = $_GET['endpoint'] ?? '';

switch ($endpoint) {
    case 'player':
        handlePlayerAPI($method);
        break;
    case 'progress':
        handleProgressAPI($method);
        break;
    case 'leaderboard':
        handleLeaderboardAPI($method);
        break;
    case 'achievements':
        handleAchievementsAPI($method);
        break;
    default:
        http_response_code(404);
        echo json_encode(['error' => 'Endpoint not found']);
}

function handlePlayerAPI($method) {
    global $gameData;
    
    if ($method === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $playerName = $input['player_name'] ?? '';
        $email = $input['email'] ?? null;
        
        if (empty($playerName)) {
            http_response_code(400);
            echo json_encode(['error' => 'Player name is required']);
            return;
        }
        
        // Check if player exists
        $existingPlayer = $gameData->getPlayerByName($playerName);
        if ($existingPlayer) {
            echo json_encode($existingPlayer);
        } else {
            $playerId = $gameData->createPlayer($playerName, $email);
            echo json_encode(['player_id' => $playerId, 'player_name' => $playerName]);
        }
    }
}

function handleProgressAPI($method) {
    global $gameData;
    
    if ($method === 'GET') {
        $playerId = $_GET['player_id'] ?? 1;
        $characterId = $_GET['character_id'] ?? '';
        
        $progress = $gameData->loadProgress($playerId, $characterId);
        echo json_encode($progress ?: ['relationship_level' => 0, 'story_index' => 0]);
        
    } elseif ($method === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        
        $playerId = $input['player_id'] ?? 1;
        $characterId = $input['character_id'] ?? '';
        $relationshipLevel = $input['relationship_level'] ?? 0;
        $storyIndex = $input['story_index'] ?? 0;
        
        $result = $gameData->saveProgress($playerId, $characterId, $relationshipLevel, $storyIndex);
        echo json_encode(['success' => $result]);
    }
}

function handleLeaderboardAPI($method) {
    global $gameData;
    
    if ($method === 'GET') {
        $characterId = $_GET['character_id'] ?? null;
        $leaderboard = $gameData->getLeaderboard($characterId);
        echo json_encode($leaderboard);
    }
}

function handleAchievementsAPI($method) {
    global $pdo;
    
    if ($method === 'GET') {
        $playerId = $_GET['player_id'] ?? 1;
        
        // Get player achievements
        $sql = "SELECT a.*, pa.earned_at 
                FROM achievements a 
                LEFT JOIN player_achievements pa ON a.achievement_id = pa.achievement_id AND pa.player_id = ?
                ORDER BY a.achievement_id";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$playerId]);
        $achievements = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode($achievements);
    }
}

// Helper function to check and award achievements
function checkAchievements($playerId, $characterId, $relationshipLevel, $storyIndex) {
    global $pdo;
    
    $achievements = [];
    
    // Check relationship achievements
    if ($relationshipLevel >= 50) {
        $achievements[] = 2; // Sahabat Baik
    }
    if ($relationshipLevel >= 100) {
        $achievements[] = 3; // Kekasih Sejati
    }
    
    // Check story completion
    if ($storyIndex >= 1) {
        $achievements[] = 1; // Perkenalan Pertama
    }
    
    // Award achievements
    foreach ($achievements as $achievementId) {
        $sql = "INSERT IGNORE INTO player_achievements (player_id, achievement_id) VALUES (?, ?)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$playerId, $achievementId]);
    }
    
    return $achievements;
}
?>
