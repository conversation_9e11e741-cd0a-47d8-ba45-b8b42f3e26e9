<?php
// Database configuration
$host = 'localhost';
$dbname = 'kisah_cinta_malaysia';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Game data management functions
class GameData {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    // Save player progress
    public function saveProgress($playerId, $characterId, $relationshipLevel, $storyIndex) {
        $sql = "INSERT INTO player_progress (player_id, character_id, relationship_level, story_index, last_played) 
                VALUES (?, ?, ?, ?, NOW()) 
                ON DUPLICATE KEY UPDATE 
                relationship_level = VALUES(relationship_level), 
                story_index = VALUES(story_index), 
                last_played = NOW()";
        
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$playerId, $characterId, $relationshipLevel, $storyIndex]);
    }
    
    // Load player progress
    public function loadProgress($playerId, $characterId) {
        $sql = "SELECT * FROM player_progress WHERE player_id = ? AND character_id = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$playerId, $characterId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    // Get player statistics
    public function getPlayerStats($playerId) {
        $sql = "SELECT 
                    character_id,
                    MAX(relationship_level) as max_relationship,
                    MAX(story_index) as max_story,
                    COUNT(*) as play_count,
                    MAX(last_played) as last_played
                FROM player_progress 
                WHERE player_id = ? 
                GROUP BY character_id";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$playerId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Create new player
    public function createPlayer($playerName, $email = null) {
        $sql = "INSERT INTO players (player_name, email, created_at) VALUES (?, ?, NOW())";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$playerName, $email]);
        return $this->pdo->lastInsertId();
    }
    
    // Get player by name
    public function getPlayerByName($playerName) {
        $sql = "SELECT * FROM players WHERE player_name = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$playerName]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    // Get leaderboard
    public function getLeaderboard($characterId = null) {
        $sql = "SELECT 
                    p.player_name,
                    pp.character_id,
                    pp.relationship_level,
                    pp.story_index,
                    pp.last_played
                FROM player_progress pp
                JOIN players p ON pp.player_id = p.player_id";
        
        if ($characterId) {
            $sql .= " WHERE pp.character_id = ?";
        }
        
        $sql .= " ORDER BY pp.relationship_level DESC, pp.story_index DESC LIMIT 10";
        
        $stmt = $this->pdo->prepare($sql);
        if ($characterId) {
            $stmt->execute([$characterId]);
        } else {
            $stmt->execute();
        }
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}

// Character information
$characterInfo = [
    'aisyah' => [
        'name' => 'Aisyah',
        'description' => 'Pelajar universiti yang bijak dan sopan',
        'personality' => ['Pandai', 'Lembut', 'Kreatif'],
        'background' => 'Aisyah adalah pelajar tahun akhir jurusan Sastera Melayu. Dia berasal dari Kelantan dan sangat menghargai budaya tradisional Malaysia.',
        'interests' => ['Membaca', 'Menulis puisi', 'Seni kaligrafi', 'Budaya tradisional'],
        'favorite_places' => ['Perpustakaan', 'Muzium', 'Taman bunga', 'Kafe buku']
    ],
    'siti' => [
        'name' => 'Siti',
        'description' => 'Pekerja pejabat yang berjaya dan berdikari',
        'personality' => ['Profesional', 'Kuat', 'Bersemangat'],
        'background' => 'Siti adalah eksekutif pemasaran digital yang berjaya. Dia berasal dari Kuala Lumpur dan sangat berdedikasi dalam kerjayanya.',
        'interests' => ['Teknologi', 'Pemasaran', 'Fotografi', 'Perjalanan'],
        'favorite_places' => ['Kafe moden', 'Pusat bandar', 'Galeri seni', 'Co-working space']
    ],
    'fatimah' => [
        'name' => 'Fatimah',
        'description' => 'Chef berbakat yang suka memasak',
        'personality' => ['Pandai masak', 'Ceria', 'Penyayang'],
        'background' => 'Fatimah adalah chef yang berbakat dan pemilik restoran kecil. Dia berasal dari Penang dan mahir dalam masakan tradisional Malaysia.',
        'interests' => ['Memasak', 'Mencuba resepi baru', 'Berkebun', 'Mengajar masakan'],
        'favorite_places' => ['Dapur', 'Pasar', 'Restoran tradisional', 'Kebun herba']
    ]
];

// Initialize game data
$gameData = new GameData($pdo);

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'save_progress':
            $playerId = $_POST['player_id'] ?? 1;
            $characterId = $_POST['character_id'] ?? '';
            $relationshipLevel = $_POST['relationship_level'] ?? 0;
            $storyIndex = $_POST['story_index'] ?? 0;
            
            $result = $gameData->saveProgress($playerId, $characterId, $relationshipLevel, $storyIndex);
            echo json_encode(['success' => $result]);
            break;
            
        case 'load_progress':
            $playerId = $_POST['player_id'] ?? 1;
            $characterId = $_POST['character_id'] ?? '';
            
            $progress = $gameData->loadProgress($playerId, $characterId);
            echo json_encode($progress ?: ['relationship_level' => 0, 'story_index' => 0]);
            break;
            
        case 'get_stats':
            $playerId = $_POST['player_id'] ?? 1;
            $stats = $gameData->getPlayerStats($playerId);
            echo json_encode($stats);
            break;
            
        case 'get_leaderboard':
            $characterId = $_POST['character_id'] ?? null;
            $leaderboard = $gameData->getLeaderboard($characterId);
            echo json_encode($leaderboard);
            break;
            
        case 'get_character_info':
            $characterId = $_POST['character_id'] ?? '';
            $info = $characterInfo[$characterId] ?? null;
            echo json_encode($info);
            break;
    }
    exit;
}
?>
